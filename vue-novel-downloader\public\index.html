<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title>某茄小说下载器 - 网页版</title>
    <meta name="description" content="功能强大的网页端小说下载工具，支持某茄小说和某猫小说平台">
    <meta name="keywords" content="小说下载,某茄小说,某猫小说,网页版下载器">
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Element UI 图标字体 -->
    <link rel="stylesheet" href="//at.alicdn.com/t/font_2143783_iq6z4ey5vu.css">
    
    <style>
      /* 页面加载动画 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: white;
        font-size: 16px;
        margin-top: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      }
    </style>
  </head>
  <body>
    <noscript>
      <strong>抱歉，某茄小说下载器需要启用JavaScript才能正常工作。请启用JavaScript后重新加载页面。</strong>
    </noscript>
    
    <!-- 页面加载动画 -->
    <div id="loading">
      <div style="text-align: center;">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载某茄小说下载器...</div>
      </div>
    </div>
    
    <div id="app"></div>
    
    <script>
      // 隐藏加载动画
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.opacity = '0';
            setTimeout(function() {
              loading.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
