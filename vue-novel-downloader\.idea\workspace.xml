<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="667c4793-327f-4b4b-b49b-f0460ef6bdb4" name="Changes" comment="初始化仓库">
      <change afterPath="$PROJECT_DIR$/.env.example" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Dockerfile" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/docker-compose.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/nginx.conf" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/package-lock.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/index.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/App.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/api/download.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/api/fanqie.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/api/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/api/novel.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/api/user.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/assets/default-cover.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/Common/EmptyState.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/Common/LoadingSpinner.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/NetworkStatus.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/router/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/store/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/store/modules/app.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/store/modules/download.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/store/modules/settings.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/store/modules/tasks.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/store/modules/user.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/styles/index.scss" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/styles/variables.scss" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/utils/auth.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/utils/error-handler.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/utils/http.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/utils/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/utils/notification.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/utils/test-api.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/utils/validation.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/views/Activation/index.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/views/FanqieDownload/index.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/start-dev.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/start-simple.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test-simplified.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/verify-fix.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vue.config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="318JmDdlMiTSzrZbiRILXWzyeNf" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="node.js.detected.package.eslint" value="true" />
    <property name="node.js.selected.package.eslint" value="(autodetect)" />
    <property name="nodejs_package_manager_path" value="npm" />
    <property name="vue.rearranger.settings.migration" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\projects\Python\learn01\番茄小说\fq_novel13\vue-novel-downloader" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="serve" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="serve" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.serve" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="667c4793-327f-4b4b-b49b-f0460ef6bdb4" name="Changes" comment="" />
      <created>1754899924968</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754899924968</updated>
      <workItem from="1754899926661" duration="9983000" />
      <workItem from="1754969337715" duration="8839000" />
      <workItem from="1755048683733" duration="10023000" />
    </task>
    <task id="LOCAL-00001" summary="初始化仓库">
      <created>1755085996873</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755085996873</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="初始化仓库" />
    <option name="LAST_COMMIT_MESSAGE" value="初始化仓库" />
  </component>
</project>