@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🚀 启动番茄小说下载器（简化版）
echo ========================================
echo.

echo 📋 检查环境...
echo.

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Node.js
    echo 💡 请先安装Node.js: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js版本: %NODE_VERSION%

echo.
echo 📦 检查依赖...

REM 检查node_modules
if not exist node_modules (
    echo ⚠️  依赖包未安装，正在安装...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖包已存在
)

echo.
echo 🔧 应用配置:
echo ├─ 开发服务器: http://localhost:8080
echo ├─ 数据模式: 模拟数据（无CORS限制）
echo ├─ 测试页面: http://localhost:8080/test-parser-refactor.html
echo └─ 功能: 完整的UI演示和下载流程
echo.

echo 💡 特性说明:
echo ├─ ✅ 无需代理服务器
echo ├─ ✅ 启动简单快速
echo ├─ ✅ 完整功能演示
echo ├─ ✅ 基于Python版本逻辑
echo └─ ⚠️  使用模拟数据（非真实内容）
echo.

echo 🚀 启动开发服务器...
echo.

REM 启动Vue开发服务器
npm run serve

echo.
echo 👋 服务器已停止
pause
