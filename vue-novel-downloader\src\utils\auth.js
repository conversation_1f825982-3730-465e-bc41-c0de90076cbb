import Cookies from 'js-cookie'

const TokenKey = 'novel_downloader_token'

export function getToken() {
  return Cookies.get(TokenKey) || localStorage.getItem(TokenKey)
}

export function setToken(token) {
  Cookies.set(Token<PERSON><PERSON>, token, { expires: 30 }) // 30天过期
  localStorage.setItem(Token<PERSON>ey, token)
}

export function removeToken() {
  Cookies.remove(TokenKey)
  localStorage.removeItem(TokenKey)
}
