/**
 * 表单验证工具
 */

// 验证规则
export const rules = {
  // 必填验证
  required: (message = '此项为必填项') => ({
    required: true,
    message,
    trigger: 'blur'
  }),

  // 邮箱验证
  email: (message = '请输入正确的邮箱地址') => ({
    type: 'email',
    message,
    trigger: 'blur'
  }),

  // 手机号验证
  phone: (message = '请输入正确的手机号') => ({
    pattern: /^1[3-9]\d{9}$/,
    message,
    trigger: 'blur'
  }),

  // URL验证
  url: (message = '请输入正确的URL地址') => ({
    type: 'url',
    message,
    trigger: 'blur'
  }),

  // 长度验证
  length: (min, max, message) => ({
    min,
    max,
    message: message || `长度在 ${min} 到 ${max} 个字符`,
    trigger: 'blur'
  }),

  // 最小长度
  minLength: (min, message) => ({
    min,
    message: message || `最少 ${min} 个字符`,
    trigger: 'blur'
  }),

  // 最大长度
  maxLength: (max, message) => ({
    max,
    message: message || `最多 ${max} 个字符`,
    trigger: 'blur'
  }),

  // 数字验证
  number: (message = '请输入数字') => ({
    type: 'number',
    message,
    trigger: 'blur'
  }),

  // 整数验证
  integer: (message = '请输入整数') => ({
    type: 'integer',
    message,
    trigger: 'blur'
  }),

  // 数字范围验证
  range: (min, max, message) => ({
    type: 'number',
    min,
    max,
    message: message || `请输入 ${min} 到 ${max} 之间的数字`,
    trigger: 'blur'
  }),

  // 自定义验证
  custom: (validator, message = '验证失败') => ({
    validator: (rule, value, callback) => {
      if (validator(value)) {
        callback()
      } else {
        callback(new Error(message))
      }
    },
    trigger: 'blur'
  })
}

// 常用验证器
export const validators = {
  // 验证小说URL
  novelUrl: (value) => {
    if (!value) return false
    return /fanqienovel\.com|qimao\.com/.test(value)
  },

  // 验证激活码
  activationCode: (value) => {
    if (!value) return false
    return /^[A-Z0-9]{6,20}$/.test(value)
  },

  // 验证机器码
  machineCode: (value) => {
    if (!value) return false
    return /^[A-Z0-9-]{10,50}$/.test(value)
  },

  // 验证文件路径
  filePath: (value) => {
    if (!value) return false
    // 简单的路径验证
    return /^[a-zA-Z]:[\\\/]/.test(value) || /^\//.test(value)
  },

  // 验证端口号
  port: (value) => {
    const port = parseInt(value)
    return port >= 1 && port <= 65535
  },

  // 验证IP地址
  ip: (value) => {
    if (!value) return false
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    return ipRegex.test(value)
  }
}

// 表单验证规则集合
export const formRules = {
  // 激活表单
  activation: {
    machineCode: [
      rules.required('请输入机器码'),
      rules.custom(validators.machineCode, '机器码格式不正确')
    ],
    activationToken: [
      rules.required('请输入激活码'),
      rules.minLength(6, '激活码至少6位'),
      rules.custom(validators.activationCode, '激活码格式不正确')
    ]
  },

  // 下载表单
  download: {
    novelUrl: [
      rules.required('请输入小说链接'),
      rules.custom(validators.novelUrl, '请输入有效的小说链接')
    ],
    format: [
      rules.required('请选择下载格式')
    ],
    savePath: [
      rules.required('请选择保存路径'),
      rules.custom(validators.filePath, '请输入有效的文件路径')
    ]
  },

  // 网络设置表单
  network: {
    proxyHost: [
      rules.custom((value) => !value || validators.ip(value), '请输入有效的IP地址')
    ],
    proxyPort: [
      rules.custom((value) => !value || validators.port(value), '请输入有效的端口号(1-65535)')
    ]
  },

  // 试用表单
  trial: {
    phone: [
      rules.required('请输入手机号'),
      rules.phone()
    ],
    code: [
      rules.required('请输入验证码'),
      rules.length(4, 6, '验证码长度为4-6位')
    ]
  }
}

// 验证工具函数
export const validate = {
  // 验证表单
  form: (formRef) => {
    return new Promise((resolve, reject) => {
      formRef.validate((valid, fields) => {
        if (valid) {
          resolve(true)
        } else {
          reject(fields)
        }
      })
    })
  },

  // 验证单个字段
  field: (formRef, fieldName) => {
    return new Promise((resolve, reject) => {
      formRef.validateField(fieldName, (errorMessage) => {
        if (errorMessage) {
          reject(errorMessage)
        } else {
          resolve(true)
        }
      })
    })
  },

  // 清除验证
  clear: (formRef, fields = null) => {
    if (fields) {
      formRef.clearValidate(fields)
    } else {
      formRef.clearValidate()
    }
  },

  // 重置表单
  reset: (formRef) => {
    formRef.resetFields()
  }
}

export default {
  rules,
  validators,
  formRules,
  validate
}
