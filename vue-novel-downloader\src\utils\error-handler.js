/**
 * 错误处理工具类
 * 基于Python版本的错误处理逻辑
 */

// 错误类型枚举
export const ErrorTypes = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  PARSE_ERROR: 'PARSE_ERROR',
  API_ERROR: 'API_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  CORS_ERROR: 'CORS_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
}

// 错误处理器类
export class ErrorHandler {
  constructor(context = {}) {
    this.context = context
    this.retryCount = 0
    this.maxRetries = 3
  }

  // 分析错误类型
  static analyzeError(error) {
    if (!error) return ErrorTypes.UNKNOWN_ERROR

    const message = error.message || error.toString()
    
    // 网络相关错误
    if (message.includes('fetch') || 
        message.includes('Network') || 
        message.includes('ERR_NETWORK') ||
        error.name === 'NetworkError') {
      return ErrorTypes.NETWORK_ERROR
    }
    
    // CORS错误
    if (message.includes('CORS') || 
        message.includes('Cross-Origin') ||
        message.includes('Access-Control-Allow-Origin')) {
      return ErrorTypes.CORS_ERROR
    }
    
    // 超时错误
    if (message.includes('timeout') || 
        message.includes('Timeout') ||
        error.name === 'AbortError') {
      return ErrorTypes.TIMEOUT_ERROR
    }
    
    // API错误
    if (message.includes('API') || 
        message.includes('HTTP') ||
        error.status) {
      return ErrorTypes.API_ERROR
    }
    
    // 解析错误
    if (message.includes('parse') || 
        message.includes('JSON') ||
        message.includes('SyntaxError')) {
      return ErrorTypes.PARSE_ERROR
    }
    
    // 验证错误
    if (message.includes('validation') || 
        message.includes('invalid') ||
        message.includes('格式')) {
      return ErrorTypes.VALIDATION_ERROR
    }
    
    return ErrorTypes.UNKNOWN_ERROR
  }

  // 获取用户友好的错误信息
  static getUserFriendlyMessage(error, errorType = null) {
    const type = errorType || ErrorHandler.analyzeError(error)
    
    const messages = {
      [ErrorTypes.NETWORK_ERROR]: {
        title: '网络连接失败',
        message: '请检查网络连接是否正常',
        suggestions: [
          '检查网络连接',
          '尝试刷新页面',
          '稍后重试'
        ]
      },
      [ErrorTypes.CORS_ERROR]: {
        title: '跨域访问限制',
        message: '浏览器阻止了跨域请求',
        suggestions: [
          '使用代理服务器',
          '尝试其他解析方式',
          '联系技术支持'
        ]
      },
      [ErrorTypes.TIMEOUT_ERROR]: {
        title: '请求超时',
        message: '服务器响应时间过长',
        suggestions: [
          '检查网络速度',
          '稍后重试',
          '尝试其他时间段'
        ]
      },
      [ErrorTypes.API_ERROR]: {
        title: 'API接口错误',
        message: '服务器返回错误响应',
        suggestions: [
          '检查输入参数',
          '稍后重试',
          '联系技术支持'
        ]
      },
      [ErrorTypes.PARSE_ERROR]: {
        title: '数据解析失败',
        message: '无法解析服务器返回的数据',
        suggestions: [
          '刷新页面重试',
          '检查数据格式',
          '联系技术支持'
        ]
      },
      [ErrorTypes.VALIDATION_ERROR]: {
        title: '输入验证失败',
        message: '输入的数据格式不正确',
        suggestions: [
          '检查输入格式',
          '参考示例格式',
          '重新输入'
        ]
      },
      [ErrorTypes.UNKNOWN_ERROR]: {
        title: '未知错误',
        message: '发生了未知的错误',
        suggestions: [
          '刷新页面重试',
          '清除浏览器缓存',
          '联系技术支持'
        ]
      }
    }
    
    return messages[type] || messages[ErrorTypes.UNKNOWN_ERROR]
  }

  // 处理书籍解析错误
  static handleBookParseError(error, bookId) {
    const errorType = ErrorHandler.analyzeError(error)
    const userMessage = ErrorHandler.getUserFriendlyMessage(error, errorType)
    
    // 根据错误类型提供特定的解决方案
    if (errorType === ErrorTypes.NETWORK_ERROR) {
      userMessage.suggestions.unshift('检查书籍ID是否正确')
    } else if (errorType === ErrorTypes.API_ERROR) {
      userMessage.suggestions.unshift('书籍可能已下架或不存在')
    }
    
    return {
      type: errorType,
      bookId,
      ...userMessage,
      originalError: error.message
    }
  }

  // 处理下载错误
  static handleDownloadError(error, context = {}) {
    const errorType = ErrorHandler.analyzeError(error)
    const userMessage = ErrorHandler.getUserFriendlyMessage(error, errorType)
    
    return {
      type: errorType,
      context,
      ...userMessage,
      originalError: error.message
    }
  }

  // 重试逻辑
  async retry(asyncFunction, maxRetries = 3, delay = 1000) {
    let lastError
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await asyncFunction()
      } catch (error) {
        lastError = error
        
        if (i === maxRetries) {
          throw error
        }
        
        // 指数退避延迟
        const retryDelay = delay * Math.pow(2, i)
        await new Promise(resolve => setTimeout(resolve, retryDelay))
      }
    }
    
    throw lastError
  }

  // 记录错误日志
  static logError(error, context = {}) {
    const errorInfo = {
      timestamp: new Date().toISOString(),
      error: error.message || error.toString(),
      stack: error.stack,
      context,
      userAgent: navigator.userAgent,
      url: window.location.href
    }
    
    console.error('Error logged:', errorInfo)
    
    // 这里可以添加错误上报逻辑
    // 例如发送到错误监控服务
  }
}

// 全局错误处理函数
export function setupGlobalErrorHandler() {
  // 捕获未处理的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    ErrorHandler.logError(event.reason, { type: 'unhandledrejection' })
  })
  
  // 捕获JavaScript错误
  window.addEventListener('error', (event) => {
    ErrorHandler.logError(event.error, { 
      type: 'javascript',
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    })
  })
}

// 导出默认实例
export default new ErrorHandler()
