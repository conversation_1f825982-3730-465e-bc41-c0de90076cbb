/**
 * 用户相关API统一管理
 * 整合所有用户相关的API接口
 */

import { http } from '@/utils/http'

// 用户API类
class UserAPI {
  constructor() {
    this.baseUrl = process.env.VUE_APP_API_BASE_URL || 'http://toolbox.zjzaki.cn/prod-api'
  }

  /**
   * 检查激活状态
   * @param {string} token - 激活码
   * @returns {Promise<Object>} 激活状态
   */
  async checkActivation(token) {
    try {
      // 这里可以实现真实的激活状态检查逻辑
      // 目前返回模拟数据
      return {
        success: true,
        isActivated: !!token,
        message: token ? '已激活' : '未激活'
      }
    } catch (error) {
      console.error('检查激活状态失败:', error)
      return {
        success: false,
        message: error.message || '检查激活状态失败'
      }
    }
  }

  /**
   * 激活用户
   * @param {Object} data - 激活数据
   * @returns {Promise<Object>} 激活结果
   */
  async activate(data) {
    const { token } = data
    return this.verifyToken(token)
  }

  /**
   * 验证激活码
   * @param {string} token - 激活码
   * @returns {Promise<Object>} 验证结果
   */
  async verifyToken(token) {
    try {
      // 第一步：验证激活码
      const response = await fetch(`${this.baseUrl}/fq_token/selectByName?tokenName=${token}`, {
        method: 'GET'
      })

      if (response.status === 200) {
        const verifyResponse = await response.json()
        const result = verifyResponse.data || verifyResponse

        // 处理不同的返回状态
        if (result.code === '500') {
          return {
            success: false,
            code: result.code,
            message: result.message || result.msg || '激活码不存在'
          }
        } else if (result.code === '520') {
          return {
            success: false,
            code: result.code,
            message: result.message || result.msg || '激活码已禁用'
          }
        } else if (result.code === '540') {
          return {
            success: false,
            code: result.code,
            message: result.message || result.msg || '访问频繁，请12小时后重试'
          }
        } else if (result.code === '510' || result.code === '200' || result.tokenCode) {
          // 激活码有效（未使用或已绑定都视为有效）
          this.saveActivationInfo(token)
          return {
            success: true,
            code: result.code || '200',
            message: result.message || result.msg || '激活成功',
            userInfo: {
              activationTime: new Date().toISOString(),
              remainingDownloads: 1000
            }
          }
        } else {
          return {
            success: false,
            code: result.code || '500',
            message: result.message || result.msg || '激活码验证失败'
          }
        }
      } else {
        throw new Error(`请求失败，状态码: ${response.status}`)
      }
    } catch (error) {
      console.error('验证激活码失败:', error)
      return {
        success: false,
        code: '500',
        message: error.message || '激活失败，请检查网络连接'
      }
    }
  }

  /**
   * 保存激活信息到本地存储
   * @param {string} token - 激活码
   */
  saveActivationInfo(token) {
    const activationData = {
      token,
      activatedAt: new Date().toISOString(),
      activationId: this.generateUUID()
    }
    localStorage.setItem('novel_downloader_activation', JSON.stringify(activationData))
  }

  /**
   * 生成UUID
   * @returns {string} UUID
   */
  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }

  /**
   * 获取用户信息
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo() {
    try {
      // 这里可以实现真实的用户信息获取逻辑
      // 目前返回模拟数据
      const activationData = localStorage.getItem('novel_downloader_activation')
      if (activationData) {
        const data = JSON.parse(activationData)
        return {
          success: true,
          data: {
            token: data.token,
            activatedAt: data.activatedAt,
            remainingDownloads: 1000
          }
        }
      } else {
        return {
          success: false,
          message: '未找到激活信息'
        }
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return {
        success: false,
        message: error.message || '获取用户信息失败'
      }
    }
  }

  /**
   * 获取剩余下载次数
   * @param {string} tokenName - 激活码
   * @returns {Promise<Object>} 剩余下载次数信息
   */
  async getDownloadCount(tokenName) {
    try {
      if (!tokenName) {
        throw new Error('激活码不能为空')
      }

      // 发送GET请求到 /fq_token/getDownloadCount
      const response = await fetch(`${this.baseUrl}/fq_token/getDownloadCount?tokenName=${tokenName}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (response.status === 200) {
        const result = await response.json()

        // 处理实际的API响应格式: {msg: '操作成功', code: 200, downloadCount: '105'}
        if (result.code === 200 || result.code === '200') {
          const downloadCount = parseInt(result.downloadCount) || 0
          return {
            success: true,
            code: result.code,
            message: result.msg || '获取成功',
            data: {
              tokenName: tokenName,
              remainingDownloads: downloadCount,
              downloadCount: downloadCount, // 保持原始字段名
              lastUpdated: new Date().toISOString()
            }
          }
        } else if (result.code === 500 || result.code === '500') {
          return {
            success: false,
            code: result.code,
            message: result.msg || '激活码不存在'
          }
        } else if (result.code === 520 || result.code === '520') {
          return {
            success: false,
            code: result.code,
            message: result.msg || '激活码已禁用'
          }
        } else {
          return {
            success: false,
            code: result.code || '500',
            message: result.msg || '获取剩余下载次数失败'
          }
        }
      } else {
        throw new Error(`请求失败，状态码: ${response.status}`)
      }
    } catch (error) {
      console.error('获取剩余下载次数失败:', error)
      return {
        success: false,
        code: '500',
        message: error.message || '获取剩余下载次数失败，请检查网络连接'
      }
    }
  }

  /**
   * 获取机器码
   * @returns {Promise<Object>} 机器码信息
   */
  async getMachineCode() {
    try {
      // 这里可以实现真实的机器码获取逻辑
      // 目前返回模拟数据
      return {
        success: true,
        data: {
          machineCode: 'MOCK-MACHINE-CODE-' + Date.now()
        }
      }
    } catch (error) {
      console.error('获取机器码失败:', error)
      return {
        success: false,
        message: error.message || '获取机器码失败'
      }
    }
  }

  /**
   * 登出用户
   * @returns {Promise<Object>} 登出结果
   */
  async logout() {
    try {
      // 清除本地存储的激活信息
      localStorage.removeItem('novel_downloader_activation')
      return {
        success: true,
        message: '登出成功'
      }
    } catch (error) {
      console.error('登出失败:', error)
      return {
        success: false,
        message: error.message || '登出失败'
      }
    }
  }
}

// 创建实例
const userAPI = new UserAPI()

export default userAPI
