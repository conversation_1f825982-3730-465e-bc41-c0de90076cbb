/**
 * 下载相关API统一管理
 * 整合所有下载相关的API接口
 */

import { http } from '@/utils/http'

// 下载API类
class DownloadAPI {
  constructor() {
    // this.baseUrl = process.env.VUE_APP_API_BASE_URL || 'http://toolbox.zjzaki.cn/prod-api'
    this.baseUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080'
  }

  /**
   * 请求下载小说 - 简化版本，不传递章节ID和标题
   * @param {Object} data - 下载请求数据
   * @returns {Promise<Object>} 下载响应
   */
  async requestNovelDownload(data) {
    try {
      const requestData = {
        tokenName: data.tokenName,
        novelId: data.novelId,
        type: "web",
        novelName: data.novelName,
        novelAuthor: data.novelAuthor,
        novelDesc: data.novelDesc,
        clientVersion: "2.2.0",
        formatChoice: data.format || 'txt'
        // 不再传递 novelChapterIds 和 novelChapterTitles
      }

      const response = await fetch(`${this.baseUrl}/fq_token/update_num_last`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      if (response.status === 200) {
        const result = await response.json()
        return result
      } else {
        throw new Error(`请求失败，状态码: ${response.status}`)
      }
    } catch (error) {
      console.error('请求下载失败:', error)
      throw error
    }
  }

  /**
   * 查询任务状态
   * @param {string} taskId - 任务ID
   * @returns {Promise<Object>} 任务状态
   */
  async checkDownloadTask(taskId) {
    try {
      const response = await fetch(`${this.baseUrl}/fq_token/query_task?taskId=${taskId}`, {
        method: 'GET'
      })

      if (response.status === 200) {
        const result = await response.json()
        return result
      } else {
        throw new Error(`请求失败，状态码: ${response.status}`)
      }
    } catch (error) {
      console.error('查询任务状态失败:', error)
      throw error
    }
  }

  /**
   * 获取用户额度
   * @param {string} token - 激活码
   * @param {string} clientVersion - 客户端版本
   * @returns {Promise<Object>} 用户额度信息
   */
  async getUserQuota(token, clientVersion = "2.2.0") {
    try {
      const response = await fetch(`${this.baseUrl}/fq_token/get_quota?tokenName=${token}&clientVersion=${clientVersion}`, {
        method: 'GET'
      })

      if (response.status === 200) {
        const result = await response.json()
        return result
      } else {
        throw new Error(`请求失败，状态码: ${response.status}`)
      }
    } catch (error) {
      console.error('获取用户额度失败:', error)
      throw error
    }
  }

  /**
   * 下载文件
   * @param {string} fileUrl - 文件URL
   * @param {string} filename - 文件名
   * @returns {Promise<boolean>} 下载是否成功
   */
  async downloadFile(fileUrl, filename) {
    try {
      if (!fileUrl) {
        throw new Error('文件URL不能为空')
      }

      // 创建下载链接
      const link = document.createElement('a')
      link.href = fileUrl
      link.download = filename
      link.target = '_blank'

      // 触发下载
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      return true
    } catch (error) {
      console.error('文件下载失败:', error)
      throw error
    }
  }

  /**
   * 获取下载历史
   * @returns {Promise<Array>} 下载历史列表
   */
  async getDownloadHistory() {
    try {
      // 这里可以实现真实的下载历史获取逻辑
      // 目前返回空数组
      return []
    } catch (error) {
      console.error('获取下载历史失败:', error)
      throw error
    }
  }

  /**
   * 轮询下载进度
   * @param {string} taskId - 任务ID
   * @param {Function} onProgress - 进度回调函数
   * @param {Function} onComplete - 完成回调函数
   * @param {Function} onError - 错误回调函数
   * @returns {Function} 停止轮询的函数
   */
  pollDownloadProgress(taskId, onProgress, onComplete, onError) {
    const pollInterval = setInterval(async () => {
      try {
        const result = await this.checkDownloadTask(taskId)

        if (result.code === 200) {
          const taskInfo = result.data || {}

          // 映射状态
          const statusMap = {
            "PENDING": "pending",
            "PROCESSING": "processing",
            "COMPLETED": "completed",
            "FAILED": "failed"
          }

          const rawStatus = taskInfo.status || ""
          const status = statusMap[rawStatus] || "unknown"

          // 处理进度值
          let progress = 0
          try {
            progress = parseInt(taskInfo.progress || 0)
          } catch (e) {
            progress = 0
          }

          const fileUrl = taskInfo.fileUrl || ""
          const errorMsg = taskInfo.errorMsg || ""

          // 调用进度回调
          if (onProgress) {
            onProgress({
              progress,
              status,
              fileUrl,
              errorMessage: errorMsg
            })
          }

          // 如果下载完成，停止轮询并调用完成回调
          if (status === 'completed' && progress >= 100) {
            clearInterval(pollInterval)
            if (onComplete) {
              onComplete({
                fileUrl,
                progress: 100,
                status: 'completed'
              })
            }
          } else if (status === 'failed') {
            clearInterval(pollInterval)
            if (onError) {
              onError(new Error(errorMsg || '下载失败'))
            }
          }
        } else {
          console.error('查询任务状态失败:', result.msg || result.message)
        }
      } catch (error) {
        console.error('轮询下载进度失败:', error)
        // 不立即停止轮询，允许网络错误后重试
      }
    }, 2000) // 每2秒轮询一次

    // 返回停止轮询的函数
    return () => clearInterval(pollInterval)
  }
}

// 创建实例
const downloadAPI = new DownloadAPI()

export default downloadAPI
