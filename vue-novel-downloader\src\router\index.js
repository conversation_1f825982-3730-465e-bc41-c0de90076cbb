import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '@/store'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    redirect: '/fanqie'
  },
  {
    path: '/fanqie',
    name: 'FanqieDownload',
    component: () => import('@/views/FanqieDownload/index.vue'),
    meta: { title: '某茄小说下载器', requiresAuth: true }
  },
  {
    path: '/activation',
    name: 'Activation',
    component: () => import('@/views/Activation/index.vue'),
    meta: { title: '激活软件' }
  },
  {
    path: '*',
    redirect: '/fanqie'
  }
]

const router = new VueRouter({
  mode: 'hash', // 使用hash模式，便于部署
  base: process.env.BASE_URL,
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 某茄小说下载器`
  }

  // 检查是否需要激活
  if (to.meta.requiresAuth) {
    const isActivated = store.getters['user/isActivated']

    if (!isActivated) {
      // 未激活，跳转到激活页面
      next('/activation')
      return
    }
  }

  next()
})

// 全局处理导航重复错误
const originalPush = router.push
const originalReplace = router.replace

router.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) {
    return originalPush.call(this, location, onResolve, onReject)
  }
  return originalPush.call(this, location).catch(err => {
    if (err.name !== 'NavigationDuplicated') {
      throw err
    }
  })
}

router.replace = function replace(location, onResolve, onReject) {
  if (onResolve || onReject) {
    return originalReplace.call(this, location, onResolve, onReject)
  }
  return originalReplace.call(this, location).catch(err => {
    if (err.name !== 'NavigationDuplicated') {
      throw err
    }
  })
}

export default router
