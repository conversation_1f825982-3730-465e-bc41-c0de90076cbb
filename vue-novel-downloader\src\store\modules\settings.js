// 设置状态管理
const state = {
  // 下载设置
  downloadSettings: {
    savePath: 'D:/Downloads/Novels',
    novelFormat: 'txt', // txt, epub
    maxWorkers: 3,
    requestTimeout: 15,
    maxRetries: 3,
    autoConvertToEpub: false,
    enableProofread: true
  },
  
  // 网络设置
  networkSettings: {
    useProxy: false,
    proxyHost: '',
    proxyPort: '',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  },
  
  // 界面设置
  uiSettings: {
    theme: 'light',
    language: 'zh-CN',
    autoCheckUpdate: true,
    showNotifications: true,
    compactMode: false
  },
  
  // 高级设置
  advancedSettings: {
    debugMode: false,
    logLevel: 'info',
    cacheEnabled: true,
    maxCacheSize: 100 // MB
  }
}

const mutations = {
  SET_DOWNLOAD_SETTINGS(state, settings) {
    state.downloadSettings = { ...state.downloadSettings, ...settings }
  },
  
  SET_NETWORK_SETTINGS(state, settings) {
    state.networkSettings = { ...state.networkSettings, ...settings }
  },
  
  SET_UI_SETTINGS(state, settings) {
    state.uiSettings = { ...state.uiSettings, ...settings }
  },
  
  SET_ADVANCED_SETTINGS(state, settings) {
    state.advancedSettings = { ...state.advancedSettings, ...settings }
  },
  
  RESET_SETTINGS(state) {
    // 重置为默认设置
    Object.assign(state, {
      downloadSettings: {
        savePath: 'D:/Downloads/Novels',
        novelFormat: 'txt',
        maxWorkers: 3,
        requestTimeout: 15,
        maxRetries: 3,
        autoConvertToEpub: false,
        enableProofread: true
      },
      networkSettings: {
        useProxy: false,
        proxyHost: '',
        proxyPort: '',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      uiSettings: {
        theme: 'light',
        language: 'zh-CN',
        autoCheckUpdate: true,
        showNotifications: true,
        compactMode: false
      },
      advancedSettings: {
        debugMode: false,
        logLevel: 'info',
        cacheEnabled: true,
        maxCacheSize: 100
      }
    })
  }
}

const actions = {
  // 加载设置
  loadSettings({ commit }) {
    try {
      const savedSettings = localStorage.getItem('novel_downloader_settings')
      if (savedSettings) {
        const settings = JSON.parse(savedSettings)
        
        if (settings.downloadSettings) {
          commit('SET_DOWNLOAD_SETTINGS', settings.downloadSettings)
        }
        if (settings.networkSettings) {
          commit('SET_NETWORK_SETTINGS', settings.networkSettings)
        }
        if (settings.uiSettings) {
          commit('SET_UI_SETTINGS', settings.uiSettings)
        }
        if (settings.advancedSettings) {
          commit('SET_ADVANCED_SETTINGS', settings.advancedSettings)
        }
      }
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  },
  
  // 保存设置
  saveSettings({ state }) {
    try {
      const settings = {
        downloadSettings: state.downloadSettings,
        networkSettings: state.networkSettings,
        uiSettings: state.uiSettings,
        advancedSettings: state.advancedSettings
      }
      localStorage.setItem('novel_downloader_settings', JSON.stringify(settings))
      return true
    } catch (error) {
      console.error('保存设置失败:', error)
      return false
    }
  },
  
  // 更新下载设置
  updateDownloadSettings({ commit, dispatch }, settings) {
    commit('SET_DOWNLOAD_SETTINGS', settings)
    dispatch('saveSettings')
  },
  
  // 更新网络设置
  updateNetworkSettings({ commit, dispatch }, settings) {
    commit('SET_NETWORK_SETTINGS', settings)
    dispatch('saveSettings')
  },
  
  // 更新界面设置
  updateUiSettings({ commit, dispatch }, settings) {
    commit('SET_UI_SETTINGS', settings)
    dispatch('saveSettings')
  },
  
  // 更新高级设置
  updateAdvancedSettings({ commit, dispatch }, settings) {
    commit('SET_ADVANCED_SETTINGS', settings)
    dispatch('saveSettings')
  },
  
  // 重置所有设置
  resetAllSettings({ commit, dispatch }) {
    commit('RESET_SETTINGS')
    dispatch('saveSettings')
  }
}

const getters = {
  downloadSettings: state => state.downloadSettings,
  networkSettings: state => state.networkSettings,
  uiSettings: state => state.uiSettings,
  advancedSettings: state => state.advancedSettings,
  
  // 便捷访问器
  savePath: state => state.downloadSettings.savePath,
  novelFormat: state => state.downloadSettings.novelFormat,
  maxWorkers: state => state.downloadSettings.maxWorkers,
  theme: state => state.uiSettings.theme,
  autoCheckUpdate: state => state.uiSettings.autoCheckUpdate
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
