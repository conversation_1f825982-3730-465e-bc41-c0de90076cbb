version: '3.8'

services:
  # 前端应用
  web:
    build: .
    container_name: novel-downloader-web
    ports:
      - "80:8080"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    networks:
      - novel-network
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 后端 API（示例）
  api:
    image: node:16-alpine
    container_name: novel-downloader-api
    working_dir: /app
    volumes:
      - ./api:/app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DB_HOST=db
      - DB_PORT=5432
      - DB_NAME=novel_db
      - DB_USER=novel_user
      - DB_PASS=novel_pass
    command: ["npm", "start"]
    restart: unless-stopped
    networks:
      - novel-network
    depends_on:
      - db
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 数据库
  db:
    image: postgres:13-alpine
    container_name: novel-downloader-db
    environment:
      - POSTGRES_DB=novel_db
      - POSTGRES_USER=novel_user
      - POSTGRES_PASSWORD=novel_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - novel-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U novel_user -d novel_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存
  redis:
    image: redis:6-alpine
    container_name: novel-downloader-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - novel-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 负载均衡器（可选）
  nginx:
    image: nginx:alpine
    container_name: novel-downloader-nginx
    ports:
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    restart: unless-stopped
    networks:
      - novel-network
    depends_on:
      - web

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  novel-network:
    driver: bridge
