import api from '@/api'
import { getToken, setToken, removeToken } from '@/utils/auth'

// 用户状态管理
const state = {
  // 激活状态
  isActivated: false,
  activationToken: getToken() || '',

  // 用户信息
  userInfo: null,

  // 激活信息
  activationInfo: null,

  // 使用统计
  usageStats: {
    totalDownloads: 0,
    remainingDownloads: 0,
    lastActiveTime: null
  },

  // 用户额度信息
  quotaInfo: {
    totalQuota: 0,
    usedQuota: 0,
    remainingQuota: 0,
    lastUpdated: null
  }
}

const mutations = {
  SET_ACTIVATION_STATUS(state, status) {
    state.isActivated = status
  },

  SET_ACTIVATION_TOKEN(state, token) {
    state.activationToken = token
    if (token) {
      setToken(token)
    } else {
      removeToken()
    }
  },

  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
  },

  SET_ACTIVATION_INFO(state, info) {
    state.activationInfo = info
  },

  SET_USAGE_STATS(state, stats) {
    state.usageStats = { ...state.usageStats, ...stats }
  },

  SET_QUOTA_INFO(state, quotaInfo) {
    state.quotaInfo = { ...state.quotaInfo, ...quotaInfo, lastUpdated: new Date() }
  }
}

const actions = {
  // 检查激活状态
  async checkActivation({ commit, state }) {
    try {
      // 首先从本地存储检查激活信息
      const activationData = localStorage.getItem('novel_downloader_activation')
      if (activationData) {
        const data = JSON.parse(activationData)

        // 后台验证激活码是否仍然有效
        try {
          const result = await api.user.verifyToken(data.token)

          if (result && result.success === true) {
            // 激活码仍然有效，自动激活
            commit('SET_ACTIVATION_TOKEN', data.token)
            commit('SET_ACTIVATION_STATUS', true)
            commit('SET_ACTIVATION_INFO', data)
            commit('SET_USER_INFO', result.userInfo)

            // 验证成功后自动获取剩余下载次数
            try {
              const downloadCountResult = await api.user.getDownloadCount(data.token)
              if (downloadCountResult.success) {
                const downloadData = downloadCountResult.data || {}
                const remainingDownloads = downloadData.remainingDownloads || 0

                // 更新使用统计
                commit('SET_USAGE_STATS', {
                  ...state.usageStats,
                  remainingDownloads: remainingDownloads,
                  lastActiveTime: downloadData.lastUpdated || new Date().toISOString()
                })

                // 更新额度信息
                commit('SET_QUOTA_INFO', {
                  ...state.quotaInfo,
                  remainingQuota: remainingDownloads,
                  lastUpdated: downloadData.lastUpdated || new Date().toISOString()
                })

              }
            } catch (downloadError) {
              console.error('验证后获取剩余下载次数失败:', downloadError)
              // 不影响验证成功的结果
            }

            return true
          } else {
            // 激活码已失效，清除本地数据
            localStorage.removeItem('novel_downloader_activation')
            commit('SET_ACTIVATION_STATUS', false)
            commit('SET_ACTIVATION_TOKEN', '')
            commit('SET_USER_INFO', null)
            commit('SET_ACTIVATION_INFO', null)
            return false
          }
        } catch (apiError) {
          // API调用失败，清除本地数据，要求重新激活
          console.log('API验证失败，清除本地激活数据:', apiError.message)
          localStorage.removeItem('novel_downloader_activation')
          commit('SET_ACTIVATION_STATUS', false)
          commit('SET_ACTIVATION_TOKEN', '')
          commit('SET_USER_INFO', null)
          commit('SET_ACTIVATION_INFO', null)
          return false
        }
      } else {
        // 没有本地激活数据，用户未激活
        console.log('没有本地激活数据，用户未激活')
        commit('SET_ACTIVATION_STATUS', false)
        commit('SET_ACTIVATION_TOKEN', '')
        commit('SET_ACTIVATION_INFO', null)
        commit('SET_USER_INFO', null)
        return false
      }
    } catch (error) {
      console.error('检查激活状态失败:', error)
      // 出错时设置为未激活状态
      commit('SET_ACTIVATION_STATUS', false)
      commit('SET_ACTIVATION_TOKEN', '')
      commit('SET_ACTIVATION_INFO', null)
      commit('SET_USER_INFO', null)
      return false
    }
  },

  // 激活软件 - 简化版本，只验证激活码
  async activateUser({ commit }, { token }) {
    try {
      // 直接调用verifyToken方法，只需要激活码
      const result = await api.user.verifyToken(token)

      // 确保result存在并且有正确的结构
      if (result && result.success === true) {
        commit('SET_ACTIVATION_TOKEN', token)
        commit('SET_ACTIVATION_STATUS', true)
        commit('SET_USER_INFO', result.userInfo)

        // 保存激活信息
        const activationInfo = {
          token,
          activatedAt: new Date().toISOString(),
          userInfo: result.userInfo
        }
        commit('SET_ACTIVATION_INFO', activationInfo)

        // 激活成功后自动获取剩余下载次数
        try {
          const downloadCountResult = await api.user.getDownloadCount(token)
          if (downloadCountResult.success) {
            const downloadData = downloadCountResult.data || {}
            const remainingDownloads = downloadData.remainingDownloads || 0

            // 更新使用统计
            commit('SET_USAGE_STATS', {
              ...state.usageStats,
              remainingDownloads: remainingDownloads,
              lastActiveTime: downloadData.lastUpdated || new Date().toISOString()
            })

            // 更新额度信息
            commit('SET_QUOTA_INFO', {
              ...state.quotaInfo,
              remainingQuota: remainingDownloads,
              lastUpdated: downloadData.lastUpdated || new Date().toISOString()
            })

          } else {
            console.warn('获取剩余下载次数失败:', downloadCountResult.message)
          }
        } catch (downloadError) {
          console.error('激活后获取剩余下载次数失败:', downloadError)
          // 不影响激活成功的结果
        }

        return { success: true, message: result.message || '激活成功！' }
      } else {
        // 处理失败情况，确保有正确的错误信息
        const message = result && result.message ? result.message : '激活失败'
        return { success: false, message }
      }
    } catch (error) {
      console.error('激活失败:', error)
      return {
        success: false,
        message: error.message || '激活失败，请检查网络连接'
      }
    }
  },



  // 注销
  logout({ commit }) {
    commit('SET_ACTIVATION_TOKEN', '')
    commit('SET_ACTIVATION_STATUS', false)
    commit('SET_USER_INFO', null)
    commit('SET_ACTIVATION_INFO', null)
    commit('SET_USAGE_STATS', {
      totalDownloads: 0,
      remainingDownloads: 0,
      lastActiveTime: null
    })
  },

  // 更新使用统计
  updateUsageStats({ commit }, stats) {
    commit('SET_USAGE_STATS', stats)
  },

  // 获取剩余下载次数 - 基于 /fq_token/getDownloadCount 接口
  async getDownloadCount({ commit, state }) {
    try {
      if (!state.activationToken) {
        throw new Error('未找到激活码')
      }

      const result = await api.user.getDownloadCount(state.activationToken)

      if (result.success) {
        const downloadData = result.data || {}

        // 更新使用统计 - 根据实际API响应格式
        const remainingDownloads = downloadData.remainingDownloads || 0
        commit('SET_USAGE_STATS', {
          ...state.usageStats,
          remainingDownloads: remainingDownloads,
          lastActiveTime: downloadData.lastUpdated || new Date().toISOString()
        })

        // 同时更新额度信息（保持兼容性）
        commit('SET_QUOTA_INFO', {
          ...state.quotaInfo,
          remainingQuota: remainingDownloads,
          lastUpdated: downloadData.lastUpdated || new Date().toISOString()
        })

        return {
          success: true,
          data: downloadData
        }
      } else {
        throw new Error(result.message || '获取剩余下载次数失败')
      }
    } catch (error) {
      console.error('获取剩余下载次数失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
}

const getters = {
  isActivated: state => state.isActivated,

  // 获取用户额度信息 - 基于Python版本的get_user_quota方法
  async getUserQuota({ commit, state }) {
    try {
      if (!state.activationToken) {
        throw new Error('未找到激活码')
      }

      const result = await api.download.getUserQuota(state.activationToken)

      if (result.code === 200 || result.code === "200") {
        const quotaData = result.data || {}

        // 更新额度信息
        commit('SET_QUOTA_INFO', {
          totalQuota: quotaData.totalQuota || 0,
          usedQuota: quotaData.usedQuota || 0,
          remainingQuota: quotaData.remainingQuota || 0
        })

        // 同时更新使用统计中的剩余下载次数
        commit('SET_USAGE_STATS', {
          ...state.usageStats,
          remainingDownloads: quotaData.remainingQuota || 0
        })

        return {
          success: true,
          quota: quotaData
        }
      } else {
        throw new Error(result.msg || result.message || '获取额度信息失败')
      }
    } catch (error) {
      console.error('获取用户额度失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  },
  activationToken: state => state.activationToken,
  userInfo: state => state.userInfo,
  activationInfo: state => state.activationInfo,
  usageStats: state => state.usageStats,
  remainingDownloads: state => state.usageStats.remainingDownloads,
  quotaInfo: state => state.quotaInfo,
  remainingQuota: state => state.quotaInfo.remainingQuota
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
