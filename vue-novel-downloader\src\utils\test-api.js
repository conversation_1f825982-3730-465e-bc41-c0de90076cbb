/**
 * API测试工具
 * 用于测试与Python版本API的兼容性
 */

// 测试配置
const TEST_CONFIG = {
  baseUrl: 'http://toolbox.zjzaki.cn/prod-api',
  testToken: 'DEMO', // 测试激活码
  testNovelId: '7143038691944959011', // 测试小说ID
  clientVersion: '2.2.0'
}

/**
 * 测试请求下载小说API
 */
async function testRequestNovelDownload() {
  console.log('=== 测试请求下载小说API ===')
  
  const requestData = {
    tokenName: TEST_CONFIG.testToken,
    novelId: TEST_CONFIG.testNovelId,
    novelName: '测试小说',
    novelAuthor: '测试作者',
    novelDesc: '这是一个测试小说的描述',
    clientVersion: TEST_CONFIG.clientVersion,
    formatChoice: 'txt',
    novelChapterIds: ['chapter1', 'chapter2', 'chapter3'],
    novelChapterTitles: ['第一章', '第二章', '第三章']
  }

  try {
    const response = await fetch(`${TEST_CONFIG.baseUrl}/fq_token/update_num_last`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })

    const result = await response.json()
    console.log('请求下载响应:', result)
    
    if (result.code === 200 || result.code === "200") {
      console.log('✅ 请求下载成功')
      return result.data?.taskId || result.taskId
    } else {
      console.log('❌ 请求下载失败:', result.msg || result.message)
      return null
    }
  } catch (error) {
    console.error('❌ 请求下载异常:', error)
    return null
  }
}

/**
 * 测试查询任务状态API
 */
async function testQueryTaskStatus(taskId) {
  console.log('=== 测试查询任务状态API ===')
  
  if (!taskId) {
    console.log('❌ 任务ID为空，跳过测试')
    return
  }

  try {
    const response = await fetch(`${TEST_CONFIG.baseUrl}/fq_token/query_task?taskId=${taskId}`, {
      method: 'GET'
    })

    const result = await response.json()
    console.log('查询任务状态响应:', result)
    
    if (result.code === 200) {
      const taskInfo = result.data || {}
      console.log('✅ 查询任务状态成功')
      console.log('任务状态:', taskInfo.status)
      console.log('任务进度:', taskInfo.progress)
      console.log('文件URL:', taskInfo.fileUrl)
      return taskInfo
    } else {
      console.log('❌ 查询任务状态失败:', result.msg || result.message)
      return null
    }
  } catch (error) {
    console.error('❌ 查询任务状态异常:', error)
    return null
  }
}

/**
 * 测试获取用户额度API
 */
async function testGetUserQuota() {
  console.log('=== 测试获取用户额度API ===')
  
  try {
    const response = await fetch(`${TEST_CONFIG.baseUrl}/fq_token/get_quota?tokenName=${TEST_CONFIG.testToken}&clientVersion=${TEST_CONFIG.clientVersion}`, {
      method: 'GET'
    })

    const result = await response.json()
    console.log('获取用户额度响应:', result)
    
    if (result.code === 200 || result.code === "200") {
      const quotaData = result.data || {}
      console.log('✅ 获取用户额度成功')
      console.log('总额度:', quotaData.totalQuota)
      console.log('已使用:', quotaData.usedQuota)
      console.log('剩余额度:', quotaData.remainingQuota)
      return quotaData
    } else {
      console.log('❌ 获取用户额度失败:', result.msg || result.message)
      return null
    }
  } catch (error) {
    console.error('❌ 获取用户额度异常:', error)
    return null
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始API兼容性测试...')
  console.log('测试配置:', TEST_CONFIG)
  
  // 测试获取用户额度
  await testGetUserQuota()
  
  // 测试请求下载
  const taskId = await testRequestNovelDownload()
  
  // 如果有任务ID，测试查询状态
  if (taskId) {
    // 等待一段时间再查询状态
    setTimeout(async () => {
      await testQueryTaskStatus(taskId)
    }, 2000)
  }
  
  console.log('✅ API兼容性测试完成')
}

// 导出测试函数
export {
  testRequestNovelDownload,
  testQueryTaskStatus,
  testGetUserQuota,
  runAllTests,
  TEST_CONFIG
}

// 如果在浏览器控制台中使用，可以直接调用
if (typeof window !== 'undefined') {
  window.testAPI = {
    testRequestNovelDownload,
    testQueryTaskStatus,
    testGetUserQuota,
    runAllTests,
    TEST_CONFIG
  }
  
  console.log('API测试工具已加载，可以在控制台中使用 window.testAPI.runAllTests() 运行测试')
}
