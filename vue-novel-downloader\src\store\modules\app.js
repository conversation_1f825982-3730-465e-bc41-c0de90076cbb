// 应用状态管理
const state = {
  // 应用版本信息
  version: '1.0.0',
  clientVersion: '1.0.0',
  
  // 侧边栏状态
  sidebarCollapsed: false,
  
  // 主题设置
  theme: 'light',
  
  // 语言设置
  language: 'zh-CN',
  
  // 更新信息
  updateInfo: null,
  hasUpdate: false,
  
  // 全局加载状态
  globalLoading: false,
  
  // 网络状态
  networkStatus: 'online'
}

const mutations = {
  SET_SIDEBAR_COLLAPSED(state, collapsed) {
    state.sidebarCollapsed = collapsed
  },
  
  SET_THEME(state, theme) {
    state.theme = theme
    // 保存到本地存储
    localStorage.setItem('theme', theme)
  },
  
  SET_LANGUAGE(state, language) {
    state.language = language
    localStorage.setItem('language', language)
  },
  
  SET_UPDATE_INFO(state, updateInfo) {
    state.updateInfo = updateInfo
    state.hasUpdate = updateInfo && updateInfo.hasUpdate
  },
  
  SET_GLOBAL_LOADING(state, loading) {
    state.globalLoading = loading
  },
  
  SET_NETWORK_STATUS(state, status) {
    state.networkStatus = status
  }
}

const actions = {
  // 切换侧边栏
  toggleSidebar({ commit, state }) {
    commit('SET_SIDEBAR_COLLAPSED', !state.sidebarCollapsed)
  },
  
  // 设置主题
  setTheme({ commit }, theme) {
    commit('SET_THEME', theme)
  },
  
  // 设置语言
  setLanguage({ commit }, language) {
    commit('SET_LANGUAGE', language)
  },
  
  // 检查更新
  async checkUpdate({ commit }) {
    try {
      // 这里调用检查更新的API
      // const response = await api.checkUpdate()
      // commit('SET_UPDATE_INFO', response.data)
      
      // 模拟检查更新
      setTimeout(() => {
        commit('SET_UPDATE_INFO', {
          hasUpdate: false,
          latestVersion: '1.0.0',
          updateLog: '当前已是最新版本'
        })
      }, 1000)
    } catch (error) {
      console.error('检查更新失败:', error)
    }
  },
  
  // 设置全局加载状态
  setGlobalLoading({ commit }, loading) {
    commit('SET_GLOBAL_LOADING', loading)
  },
  
  // 初始化应用设置
  initAppSettings({ commit }) {
    // 从本地存储加载设置
    const theme = localStorage.getItem('theme') || 'light'
    const language = localStorage.getItem('language') || 'zh-CN'
    
    commit('SET_THEME', theme)
    commit('SET_LANGUAGE', language)
  }
}

const getters = {
  sidebarCollapsed: state => state.sidebarCollapsed,
  theme: state => state.theme,
  language: state => state.language,
  hasUpdate: state => state.hasUpdate,
  updateInfo: state => state.updateInfo,
  globalLoading: state => state.globalLoading,
  networkStatus: state => state.networkStatus,
  isOnline: state => state.networkStatus === 'online'
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
